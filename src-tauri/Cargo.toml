[package]
name = "dj-music-organizer"
version = "0.1.0"
description = "A Tauri App for DJ Music Organization"
authors = ["you"]
license = ""
repository = ""
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "2.0", features = [] }

[dependencies]
tauri = { version = "2.0", features = [] }
tauri-plugin-fs = "2.0"
tauri-plugin-dialog = "2.0"
tauri-plugin-shell = "2.0"
tauri-plugin-log = "2.0"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
anyhow = "1.0"
tokio = { version = "1.0", features = ["full"] }
uuid = { version = "1.0", features = ["v4"] }
chrono = { version = "0.4", features = ["serde"] }
log = "0.4"

# Audio processing dependencies
id3 = "1.13"
metaflac = "0.2"
mp4ameta = "0.11"
symphonia = { version = "0.5", features = ["all"] }

# File system utilities
walkdir = "2.4"
glob = "0.3"
regex = "1.10"

[lib]
name = "app_lib"
crate-type = ["cdylib", "rlib"]
