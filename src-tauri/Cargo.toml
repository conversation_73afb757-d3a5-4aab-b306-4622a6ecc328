[package]
name = "dj-music-organizer"
version = "0.1.0"
description = "AI-powered DJ Music Organization System"
authors = ["DJ Music Organizer Team"]
license = "MIT"
repository = ""
edition = "2021"
rust-version = "1.77.2"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
name = "app_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2.3.0", features = [] }

[dependencies]
serde_json = "1.0"
serde = { version = "1.0", features = ["derive"] }
log = "0.4"
tauri = { version = "2.6.2", features = [] }
tauri-plugin-log = "2"
tauri-plugin-fs = "2"
tauri-plugin-dialog = "2"
tauri-plugin-shell = "2"

# Audio processing dependencies
symphonia = { version = "0.5", features = ["all"] }
id3 = "1.13"
metaflac = "0.2"
mp4ameta = "0.11"

# File system and path utilities
walkdir = "2.4"
glob = "0.3"
regex = "1.10"

# Data processing and serialization
tokio = { version = "1.0", features = ["full"] }
uuid = { version = "1.0", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }
anyhow = "1.0"

# Machine learning and audio analysis (placeholder for future implementation)
# We'll add specific ML crates as we implement the AI features
