{"$schema": "https://schema.tauri.app/config/2", "productName": "DJ Music Organizer", "version": "0.1.0", "identifier": "com.djorganizer.app", "build": {"frontendDist": "../frontend/dist", "devUrl": "http://localhost:5173", "beforeBuildCommand": "cd ../frontend && npm run build"}, "app": {"windows": [{"title": "DJ Music Organizer", "width": 1400, "height": 900, "minWidth": 1200, "minHeight": 800, "resizable": true, "fullscreen": false}], "security": {"csp": null}}, "plugins": {"fs": {"requireLiteralLeadingDot": false}, "dialog": {"open": true, "save": true}, "shell": {"open": true}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}