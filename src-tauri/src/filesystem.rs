use std::path::{Path, PathBuf};
use std::fs;
use serde::{Deserialize, Serialize};
use anyhow::{Result, anyhow};
use crate::audio::{AudioProcessor, AudioFile};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DirectoryFilter {
    pub include_patterns: Vec<String>,
    pub exclude_patterns: Vec<String>,
    pub file_types: Vec<String>,
    pub min_file_size: Option<u64>,
    pub max_file_size: Option<u64>,
    pub max_depth: Option<usize>,
    pub regex_patterns: Vec<String>,
}

impl Default for DirectoryFilter {
    fn default() -> Self {
        Self {
            include_patterns: vec!["*".to_string()],
            exclude_patterns: vec![],
            file_types: vec![
                "mp3".to_string(),
                "flac".to_string(),
                "wav".to_string(),
                "m4a".to_string(),
                "aac".to_string(),
                "aiff".to_string(),
                "ogg".to_string(),                            
            ],
            min_file_size: Some(1024),               // 1KB minimum
            max_file_size: Some(1024 * 1024 * 1024), // 1GB maximum
            max_depth: None,
            regex_patterns: vec![],
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DirectoryStructure {
    pub path: String,
    pub name: String,
    pub is_directory: bool,
    pub children: Vec<DirectoryStructure>,
    pub audio_files: Vec<AudioFile>,
    pub total_files: usize,
    pub total_size: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScanProgress {
    pub current_path: String,
    pub files_processed: usize,
    pub total_files: usize,
    pub bytes_processed: u64,
    pub total_bytes: u64,
    pub percentage: f64,
    pub status: String,
}

pub struct FileSystemScanner {
    filter: DirectoryFilter,
}

impl FileSystemScanner {
    pub fn new(filter: DirectoryFilter) -> Self {
        Self { filter }
    }

    /// Scan a directory and return all audio files with metadata
    pub fn scan_directory<P: AsRef<Path>>(&self, root_path: P) -> Result<Vec<AudioFile>> {
        let root_path = root_path.as_ref();

        if !root_path.exists() {
            return Err(anyhow!("Directory does not exist: {}", root_path.display()));
        }

        if !root_path.is_dir() {
            return Err(anyhow!("Path is not a directory: {}", root_path.display()));
        }

audio_files = Vec::new();
        let walker = WalkDir::new(root_path);

        let walker = if let Some(max_depth) = self.filter.max_depth {
            walker.max_depth(max_depth)
        } else {
            walker
        };

ker.into_iter().filter_map(|e| e.ok()) {
            let path = entry.path();

            if path.is_file() && self.should_include_file(path)? {
                if let Ok(metadata) = AudioProcessor::extract_metadata(path) {
                    let audio_file = A
                            
                            udioFile {
                        path: path.to_string_lossy().to_string(),
                        filename: path
                            .file_name()
                            .unwrap_or_default()
                            .to_string_lossy()
                            .to_string(),
                        metadata,
                        analysis: None, // Will be populated later during analysis
                    };
                    audio_files.push(audio_file);
                }
            }
        }

        Ok(audio_files)
    }

    /// Get directory structure with audio file counts
    pub fn get_directory_structure<P: AsRef<Path>>(&self, root_path: P) -> Result<DirectoryStructure> {
        let root_path = root_path.as_ref();
        self.build_directory_structure(root_path, 0)
    }

    fn build_directory_structure(
        &self,
        path: &Path,
        current_depth: usize,
    ) -> Result<DirectoryStructure> {
        let name = path
            .file_name()
            .unwrap_or_default()
            .to_string_lossy()
            .to_string();

        let mut structure = DirectoryStructure {
th.to_string_lossy().to_string(),
            name,
            is_directory: path.is_dir(),
                           
                       
            children: Vec::new(),
            audio_files: Vec::new(),
            total_files: 0,
            total_size: 0,
        };

ir() {
            // Check depth limit
            if let Some(max_depth) = self.filter.max
                                    
                           _depth {
                       
                if current_depth >= max_depth {
                    return Ok(structure);
                }
            }

            if let Ok(entries) = fs::read_dir(path) {
                for entry in entries.filter_map(|e| e.ok()) {
                    let entry_path = entry.path();

                                    
                    if entry_path.is_dir() {
                        if let Ok(child_structure) =
                            self.build_directory_structure(&entry_path, current_depth + 1)
                        {
                            structure.total_files += child_structure.total_files;
                            structure.total_size += child_structure.total_size;
structure.children.push(child_structure);
                        }
                    } else if self.should_include_file(&entry_path).unwrap_or(false) {
                        if let Ok(metadata) = AudioProcessor::extract_metadata(&entry_path) {
                            let audio_file = AudioFile {
                                path: entry_path.to_string_lossy().to_string(),
                                filename: entry_path
                                    .file_name()
                                    .unwrap_or_default()
                                    .to_string_lossy()
                                    .to_string(),
                                metadata: metadata.clone(),
                                analysis: None,
                            };

                            structure.total_files += 1;
                            structure.total_size += metadata.file_size;
                            structure.audio_files.push(audio_file);
                        }
                    }
    }
            }
        }

        Ok(structure)
    }

    /// Check if a file should be included based on filters
    fn should_include_file(&self, path: &Path) -> Result<bool> {
        // Check if it's a supported audio format
        if !AudioProcessor::is_supported_format(path) {
            return Ok(false);
        }

        // Check file extension
        if let Some(extension) = path.extension() {
            if let Some(ext_str) = extension.to_str() {
                if !self.filter.file_types.contains(&ext_str.to_lowercase()) {
                    return Ok(false);
                }
            }
        }

        // Check file size
        if let Ok(metadata) = fs::metadata(path) {
            let file_size = metadata.len();

            if let Some(min_size) = self.filter.min_file_size {
                if file_size < min_size {
                    return Ok(false);
                }
            }

            if let Some(max_size) = self.filter.max_file_size {
                if file_size > max_size {
                    return Ok(false);
                }
            }
        }

        // Check include patterns
        let filename = path.file_name()
            .and_then(|n| n.to_str())
            .unwrap_or("");

        let mut included = false;
        for pattern in &self.filter.include_patterns {
            if let Ok(glob_pattern) = Pattern::new(pattern) {
                if glob_pattern.matches(filename) {
                    included = true;
                    break;
                }
            }
        }

        if !included && !self.filter.include_patterns.is_empty() {
            return Ok(false);
        }

        // Check exclude patterns
        for pattern in &self.filter.exclude_patterns {
            if let Ok(glob_pattern) = Pattern::new(pattern) {
                if glob_pattern.matches(filename) {
                    return Ok(false);
                }
            }
        }

        // Check regex patterns
        for regex_str in &self.filter.regex_patterns {
            if let Ok(regex) = Regex::new(regex_str) {
                if regex.is_match(filename) {
                    return Ok(true);
                }
            }
        }

        Ok(true)
    }

    /// Count total files that would be processed
    pub fn count_files<P: AsRef<Path>>(&self, root_path: P) -> Result<usize> {
        let root_path = root_path.as_ref();
        let mut count = 0;

        let walker = WalkDir::new(root_path);
        let walker = if let Some(max_depth) = self.filter.max_depth {
            walker.max_depth(max_depth)
        } else {
            walker
        };

        for entry in walker.into_iter().filter_map(|e| e.ok()) {
            let path = entry.path();
    if path.is_file() && self.should_include_file(path)? {
                count += 1;
            }
        }

        Ok(count)
    }

    /// Get estimated total size of files to be processed
    pub fn estimate_total_size<P: AsRef<Path>>(&self, root_path: P) -> Result<u64> {
        let root_path = root_path.as_ref();
        let mut total_size = 0;

        let walker = WalkDir::new(root_path);
        let walker = if let Some(max_depth) = self.filter.max_depth {
            walker.max_depth(max_depth)
        } else {
            walker
        };

        for entry in walker.into_iter().filter_map(|e| e.ok()) {
            let path = entry.path();
            if path.is_file() && self.should_include_file(path)? {
                if let Ok(metadata) = fs::metadata(path) {
                    total_size += metadata.len();
                }
            }
        }

        Ok(total_size)
    }
}

/// Utility functions for file operations
pub struct FileOperations;

impl FileOperations {
    /// Create a backup of a file before modification
    pub fn create_backup<P: AsRef<Path>>(file_path: P) -> Result<PathBuf> {
        let file_path = file_path.as_ref();
        let backup_path = file_path.with_extension(
            format!("{}.backup",
                file_path.extension()
                    .and_then(|ext| ext.to_str())
                    .unwrap_or(""))
        );

        fs::copy(file_path, &backup_path)?;
        Ok(backup_path)
    }

    /// Move a file to a new location
    pub fn move_file<P: AsRef<Path>, Q: AsRef<Path>>(from: P, to: Q) -> Result<()> {
        let from = from.as_ref();
        let to = to.as_ref();

        // Create parent directory if it doesn't exist
        if let Some(parent) = to.parent() {
            fs::create_dir_all(parent)?;
        }

        fs::rename(from, to)?;
        Ok(())
    }

    /// Copy a file to a new location
    pub fn copy_file<P: AsRef<Path>, Q: AsRef<Path>>(from: P, to: Q) -> Result<()> {
        let from = from.as_ref();
        let to = to.as_ref();

        // Create parent directory if it doesn't exist
        if let Some(parent) = to.parent() {
            fs::create_dir_all(parent)?;
        }

        fs::copy(from, to)?;
        Ok(())
    }

    /// Check if a path is safe to operate on (not system directories)
    pub fn is_safe_path<P: AsRef<Path>>(path: P) -> bool {
        let path = path.as_ref();
        let path_str = path.to_string_lossy().to_lowercase();

        // Avoid system directories
        let unsafe_patterns = [
            "/system", "/windows", "/program files", "/boot",
            "c:\\windows", "c:\\program files", "/usr/bin", "/bin", "/sbin"
        ];

        !unsafe_patterns.iter().any(|pattern| path_str.starts_with(pattern))
    }
}
