use anyhow::{anyhow, Result};
use id3::{Tag, TagLike};
use metaflac::Tag as FlacTag;
use mp4ameta::Tag as Mp4Tag;
use serde::{Deserialize, Serialize};
use std::path::Path;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AudioMetadata {
    pub title: Option<String>,
    pub artist: Option<String>,
    pub album: Option<String>,
    pub year: Option<u32>,
    pub genre: Option<String>,
    pub track: Option<u32>,
    pub duration: Option<f64>,
    pub bitrate: Option<u32>,
    pub sample_rate: Option<u32>,
    pub channels: Option<u16>,
    pub format: String,
    pub file_size: u64,
    pub bpm: Option<f64>,
    pub key: Option<String>,
    pub comment: Option<String>,
    pub energy_level: Option<String>,
    pub sub_genre: Option<String>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AudioFile {
    pub path: String,
    pub filename: String,
    pub metadata: AudioMetadata,
    pub analysis: Option<AudioAnalysis>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct AudioAnalysis {
    pub detected_bpm: Option<f64>,
    pub detected_key: Option<String>,
    pub energy_level: Option<f64>,
    pub has_vocals: Option<bool>,
    pub spectral_features: Option<SpectralFeatures>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SpectralFeatures {
    pub low_freq_energy: f64,
    pub mid_freq_energy: f64,
    pub high_freq_energy: f64,
    pub spectral_centroid: f64,
    pub spectral_rolloff: f64,
}

pub struct AudioProcessor;

impl AudioProcessor {
    pub fn new() -> Self {
        Self
    }

    /// Extract metadata from an audio file
    pub fn extract_metadata<P: AsRef<Path>>(path: P) -> Result<AudioMetadata> {
        let path = path.as_ref();
        let extension = path
            .extension()
            .and_then(|ext| ext.to_str())
            .unwrap_or("")
            .to_lowercase();

        let file_size = std::fs::metadata(path)?.len();

        match extension.as_str() {
            "mp3" => Self::extract_mp3_metadata(path, file_size),
            "flac" => Self::extract_flac_metadata(path, file_size),
            "m4a" | "mp4" | "aac" => Self::extract_mp4_metadata(path, file_size),
            "wav" | "aiff" => Self::extract_wav_metadata(path, file_size),
            _ => Ok(AudioMetadata {
                title: None,
                artist: None,
                album: None,
                year: None,
                genre: None,
                track: None,
                duration: None,
                bitrate: None,
                sample_rate: None,
                channels: None,
                format: extension,
                file_size,
                bpm: None,
                key: None,
                comment: None,
                energy_level: None,
                sub_genre: None,
            }),
        }
    }

    fn extract_mp3_metadata<P: AsRef<Path>>(path: P, file_size: u64) -> Result<AudioMetadata> {
        let tag = Tag::read_from_path(&path).unwrap_or_else(|_| Tag::new());

        // Extract energy level and sub-genre from comment field
        let comment = tag.comments().next().map(|c| c.text.clone());
        let (energy_level, sub_genre) = Self::parse_comment_field(&comment);

        Ok(AudioMetadata {
            title: tag.title().map(|s| s.to_string()),
            artist: tag.artist().map(|s| s.to_string()),
            album: tag.album().map(|s| s.to_string()),
            year: tag.year().map(|y| y as u32),
            genre: tag.genre().map(|s| s.to_string()),
            track: tag.track(),
            duration: tag.duration().map(|d| d as f64),
            bitrate: None, // Would need audio analysis for this
            sample_rate: None,
            channels: None,
            format: "mp3".to_string(),
            file_size,
            bpm: Self::extract_bpm_from_filename(&path),
            key: Self::extract_key_from_filename(&path),
            comment,
            energy_level,
            sub_genre,
        })
    }

    fn extract_flac_metadata<P: AsRef<Path>>(path: P, file_size: u64) -> Result<AudioMetadata> {
        let tag = FlacTag::read_from_path(&path).unwrap_or_else(|_| FlacTag::new());

        let comment = tag
            .get_vorbis("COMMENT")
            .and_then(|mut iter| iter.next())
            .map(|s| s.to_string());
        let (energy_level, sub_genre) = Self::parse_comment_field(&comment);

        Ok(AudioMetadata {
            title: tag
                .get_vorbis("TITLE")
                .and_then(|mut iter| iter.next())
                .map(|s| s.to_string()),
            artist: tag
                .get_vorbis("ARTIST")
                .and_then(|mut iter| iter.next())
                .map(|s| s.to_string()),
            album: tag
                .get_vorbis("ALBUM")
                .and_then(|mut iter| iter.next())
                .map(|s| s.to_string()),
            year: tag
                .get_vorbis("DATE")
                .and_then(|mut iter| iter.next())
                .and_then(|s| s.parse().ok()),
            genre: tag
                .get_vorbis("GENRE")
                .and_then(|mut iter| iter.next())
                .map(|s| s.to_string()),
            track: tag
                .get_vorbis("TRACKNUMBER")
                .and_then(|mut iter| iter.next())
                .and_then(|s| s.parse().ok()),
            duration: None, // Would need audio analysis
            bitrate: None,
            sample_rate: None,
            channels: None,
            format: "flac".to_string(),
            file_size,
            bpm: Self::extract_bpm_from_filename(&path),
            key: Self::extract_key_from_filename(&path),
            comment,
            energy_level,
            sub_genre,
        })
    }

    fn extract_mp4_metadata<P: AsRef<Path>>(path: P, file_size: u64) -> Result<AudioMetadata> {
        let tag = Mp4Tag::read_from_path(&path).unwrap_or_else(|_| Mp4Tag::default());

        let comment = tag.comment().map(|s| s.to_string());
        let (energy_level, sub_genre) = Self::parse_comment_field(&comment);

        Ok(AudioMetadata {
            title: tag.title().map(|s| s.to_string()),
            artist: tag.artist().map(|s| s.to_string()),
            album: tag.album().map(|s| s.to_string()),
            year: tag.year().map(|s| s.parse().unwrap_or(0)),
            genre: tag.genre().map(|s| s.to_string()),
            track: tag.track_number().map(|t| t as u32),
            duration: None,
            bitrate: None,
            sample_rate: None,
            channels: None,
            format: "m4a".to_string(),
            file_size,
            bpm: Self::extract_bpm_from_filename(&path),
            key: Self::extract_key_from_filename(&path),
            comment,
            energy_level,
            sub_genre,
        })
    }

    fn extract_wav_metadata<P: AsRef<Path>>(path: P, file_size: u64) -> Result<AudioMetadata> {
        // WAV files typically don't have extensive metadata
        Ok(AudioMetadata {
            title: None,
            artist: None,
            album: None,
            year: None,
            genre: None,
            track: None,
            duration: None,
            bitrate: None,
            sample_rate: None,
            channels: None,
            format: "wav".to_string(),
            file_size,
            bpm: Self::extract_bpm_from_filename(&path),
            key: Self::extract_key_from_filename(&path),
            comment: None,
            energy_level: None,
            sub_genre: None,
        })
    }

    /// Parse comment field to extract energy level and sub-genre
    /// Expected format: [vocal info]. [sub-genre]. [description/context]
    fn parse_comment_field(comment: &Option<String>) -> (Option<String>, Option<String>) {
        if let Some(comment) = comment {
            let parts: Vec<&str> = comment.split(". ").collect();
            if parts.len() >= 2 {
                let sub_genre = parts[1].trim_matches(['[', ']']);
                return (None, Some(sub_genre.to_string()));
            }
        }
        (None, None)
    }

    /// Extract BPM from filename (e.g., "Track Name [128 Amin] [M].mp3")
    fn extract_bpm_from_filename<P: AsRef<Path>>(path: P) -> Option<f64> {
        let filename = path.as_ref().file_stem()?.to_str()?;

        // Look for BPM pattern like [128 Amin] or [130]
        if let Some(start) = filename.find('[') {
            if let Some(end) = filename[start..].find(']') {
                let bracket_content = &filename[start + 1..start + end];
                if let Some(space_pos) = bracket_content.find(' ') {
                    let bpm_str = &bracket_content[..space_pos];
                    return bpm_str.parse().ok();
                } else {
                    return bracket_content.parse().ok();
                }
            }
        }
        None
    }

    /// Extract musical key from filename (e.g., "Track Name [128 Amin] [M].mp3")
    fn extract_key_from_filename<P: AsRef<Path>>(path: P) -> Option<String> {
        let filename = path.as_ref().file_stem()?.to_str()?;

        // Look for key pattern like [128 Amin]
        if let Some(start) = filename.find('[') {
            if let Some(end) = filename[start..].find(']') {
                let bracket_content = &filename[start + 1..start + end];
                if let Some(space_pos) = bracket_content.find(' ') {
                    let key_str = &bracket_content[space_pos + 1..];
                    if key_str.len() >= 2 && (key_str.contains("maj") || key_str.contains("min")) {
                        return Some(key_str.to_string());
                    }
                }
            }
        }
        None
    }

    /// Check if a file is a supported audio format
    pub fn is_supported_format<P: AsRef<Path>>(path: P) -> bool {
        if let Some(extension) = path.as_ref().extension() {
            if let Some(ext_str) = extension.to_str() {
                matches!(
                    ext_str.to_lowercase().as_str(),
                    "mp3" | "flac" | "wav" | "m4a" | "mp4" | "aac" | "aiff" | "ogg"
                )
            } else {
                false
            }
        } else {
            false
        }
    }
}
