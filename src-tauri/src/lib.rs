mod audio;
mod commands;
mod filesystem;

use commands::ScanSessions;
use std::collections::HashMap;
use std::sync::{Arc, Mutex};

// Learn more about Tauri commands at https://tauri.app/v1/guides/features/command
#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    // Initialize shared state for scan sessions
    let scan_sessions: ScanSessions = Arc::new(Mutex::new(HashMap::new()));

    tauri::Builder::default()
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_dialog::init())
        .plugin(tauri_plugin_shell::init())
        .manage(scan_sessions)
        .setup(|app| {
            if cfg!(debug_assertions) {
                app.handle().plugin(
                    tauri_plugin_log::Builder::default()
                        .level(log::LevelFilter::Info)
                        .build(),
                )?;
            }
            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            greet,
            commands::select_directory,
            commands::scan_directory,
            commands::get_scan_progress,
            commands::get_scan_results,
            commands::get_directory_structure,
            commands::extract_audio_metadata,
            commands::is_supported_audio_format,
            commands::validate_directory_path,
            commands::get_directory_info,
            commands::create_backup,
            commands::move_audio_file,
            commands::copy_audio_file,
            commands::cancel_scan,
            commands::cleanup_scan_session,
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
