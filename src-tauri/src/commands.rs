use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::Path;
use std::sync::{Arc, Mutex};
use tauri::State;
use uuid::Uuid;

use crate::audio::{AudioFile, AudioMetadata};
use crate::filesystem::{DirectoryFilter, DirectoryStructure, FileSystemScanner, ScanProgress};

#[derive(<PERSON>bu<PERSON>, <PERSON>lone, Serialize, Deserialize)]
pub struct ScanSession {
    pub id: String,
    pub root_path: String,
    pub filter: DirectoryFilter,
    pub progress: ScanProgress,
    pub results: Vec<AudioFile>,
    pub completed: bool,
}

pub type ScanSessions = Arc<Mutex<HashMap<String, ScanSession>>>;

#[tauri::command]
pub async fn select_directory() -> Result<Option<String>, String> {
    use tauri_plugin_dialog::{DialogExt, MessageDialogKind};

    // This would be implemented with the dialog plugin
    // For now, return a placeholder
    Ok(Some("/Users/<USER>/Music".to_string()))
}

#[tauri::command]
pub async fn scan_directory(
    root_path: String,
    filter: Option<DirectoryFilter>,
    sessions: State<'_, ScanSessions>,
) -> Result<String, String> {
    let filter = filter.unwrap_or_default();
    let session_id = Uuid::new_v4().to_string();

    // Validate path
    let path = Path::new(&root_path);
    if !path.exists() {
        return Err("Directory does not exist".to_string());
    }

    if !path.is_dir() {
        return Err("Path is not a directory".to_string());
    }

    // Create scanner
    let scanner = FileSystemScanner::new(filter.clone());

    // Estimate total work
    let total_files = scanner.count_files(&root_path).map_err(|e| e.to_string())?;
    let total_bytes = scanner
        .estimate_total_size(&root_path)
        .map_err(|e| e.to_string())?;

    // Create session
    let session = ScanSession {
        id: session_id.clone(),
        root_path: root_path.clone(),
        filter: filter.clone(),
        progress: ScanProgress {
            current_path: root_path.clone(),
            files_processed: 0,
            total_files,
            bytes_processed: 0,
            total_bytes,
            percentage: 0.0,
            status: "Starting scan...".to_string(),
        },
        results: Vec::new(),
        completed: false,
    };

    // Store session
    {
        let mut sessions_guard = sessions.lock().unwrap();
        sessions_guard.insert(session_id.clone(), session);
    }

    // Start scanning in background
    let sessions_clone = sessions.inner().clone();
    let session_id_clone = session_id.clone();
    let root_path_clone = root_path.clone();

    tokio::spawn(async move {
        let scanner = FileSystemScanner::new(filter);

        match scanner.scan_directory(&root_path_clone) {
            Ok(audio_files) => {
                let mut sessions_guard = sessions_clone.lock().unwrap();
                if let Some(session) = sessions_guard.get_mut(&session_id_clone) {
                    session.results = audio_files;
                    session.completed = true;
                    session.progress.percentage = 100.0;
                    session.progress.status = "Scan completed".to_string();
                }
            }
            Err(e) => {
                let mut sessions_guard = sessions_clone.lock().unwrap();
                if let Some(session) = sessions_guard.get_mut(&session_id_clone) {
                    session.progress.status = format!("Error: {}", e);
                }
            }
        }
    });

    Ok(session_id)
}

#[tauri::command]
pub async fn get_scan_progress(
    session_id: String,
    sessions: State<'_, ScanSessions>,
) -> Result<ScanProgress, String> {
    let sessions_guard = sessions.lock().unwrap();

    if let Some(session) = sessions_guard.get(&session_id) {
        Ok(session.progress.clone())
    } else {
        Err("Session not found".to_string())
    }
}

#[tauri::command]
pub async fn get_scan_results(
    session_id: String,
    sessions: State<'_, ScanSessions>,
) -> Result<Vec<AudioFile>, String> {
    let sessions_guard = sessions.lock().unwrap();

    if let Some(session) = sessions_guard.get(&session_id) {
        if session.completed {
            Ok(session.results.clone())
        } else {
            Err("Scan not completed yet".to_string())
        }
    } else {
        Err("Session not found".to_string())
    }
}

#[tauri::command]
pub async fn get_directory_structure(
    root_path: String,
    filter: Option<DirectoryFilter>,
) -> Result<DirectoryStructure, String> {
    let filter = filter.unwrap_or_default();
    let scanner = FileSystemScanner::new(filter);

    scanner
        .get_directory_structure(&root_path)
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn extract_audio_metadata(file_path: String) -> Result<AudioMetadata, String> {
    use crate::audio::AudioProcessor;

    AudioProcessor::extract_metadata(&file_path).map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn is_supported_audio_format(file_path: String) -> Result<bool, String> {
    use crate::audio::AudioProcessor;

    Ok(AudioProcessor::is_supported_format(&file_path))
}

#[tauri::command]
pub async fn validate_directory_path(path: String) -> Result<bool, String> {
    let path = Path::new(&path);
    Ok(path.exists() && path.is_dir())
}

#[tauri::command]
pub async fn get_directory_info(path: String) -> Result<DirectoryInfo, String> {
    let path = Path::new(&path);

    if !path.exists() {
        return Err("Directory does not exist".to_string());
    }

    if !path.is_dir() {
        return Err("Path is not a directory".to_string());
    }

    let mut total_files = 0;
    let mut audio_files = 0;
    let mut total_size = 0;

    if let Ok(entries) = std::fs::read_dir(path) {
        for entry in entries.filter_map(|e| e.ok()) {
            let entry_path = entry.path();
            if entry_path.is_file() {
                total_files += 1;

                if let Ok(metadata) = std::fs::metadata(&entry_path) {
                    total_size += metadata.len();
                }

                if crate::audio::AudioProcessor::is_supported_format(&entry_path) {
                    audio_files += 1;
                }
            }
        }
    }

    Ok(DirectoryInfo {
        path: path.to_string_lossy().to_string(),
        total_files,
        audio_files,
        total_size,
        readable: true,
    })
}

#[derive(Debug, Serialize, Deserialize)]
pub struct DirectoryInfo {
    pub path: String,
    pub total_files: usize,
    pub audio_files: usize,
    pub total_size: u64,
    pub readable: bool,
}

#[tauri::command]
pub async fn create_backup(file_path: String) -> Result<String, String> {
    use crate::filesystem::FileOperations;

    FileOperations::create_backup(&file_path)
        .map(|backup_path| backup_path.to_string_lossy().to_string())
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn move_audio_file(from_path: String, to_path: String) -> Result<(), String> {
    use crate::filesystem::FileOperations;

    // Safety check
    if !FileOperations::is_safe_path(&from_path) || !FileOperations::is_safe_path(&to_path) {
        return Err("Unsafe file path detected".to_string());
    }

    FileOperations::move_file(&from_path, &to_path).map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn copy_audio_file(from_path: String, to_path: String) -> Result<(), String> {
    use crate::filesystem::FileOperations;

    // Safety check
    if !FileOperations::is_safe_path(&from_path) || !FileOperations::is_safe_path(&to_path) {
        return Err("Unsafe file path detected".to_string());
    }

    FileOperations::copy_file(&from_path, &to_path).map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn cancel_scan(
    session_id: String,
    sessions: State<'_, ScanSessions>,
) -> Result<(), String> {
    let mut sessions_guard = sessions.lock().unwrap();

    if let Some(session) = sessions_guard.get_mut(&session_id) {
        session.progress.status = "Cancelled".to_string();
        // In a real implementation, we'd also signal the background task to stop
        Ok(())
    } else {
        Err("Session not found".to_string())
    }
}

#[tauri::command]
pub async fn cleanup_scan_session(
    session_id: String,
    sessions: State<'_, ScanSessions>,
) -> Result<(), String> {
    let mut sessions_guard = sessions.lock().unwrap();
    sessions_guard.remove(&session_id);
    Ok(())
}
