["/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/webview/autogenerated/commands/clear_all_browsing_data.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/webview/autogenerated/commands/create_webview.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/webview/autogenerated/commands/create_webview_window.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/webview/autogenerated/commands/get_all_webviews.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/webview/autogenerated/commands/internal_toggle_devtools.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/webview/autogenerated/commands/print.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/webview/autogenerated/commands/reparent.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/webview/autogenerated/commands/set_webview_auto_resize.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/webview/autogenerated/commands/set_webview_background_color.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/webview/autogenerated/commands/set_webview_focus.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/webview/autogenerated/commands/set_webview_position.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/webview/autogenerated/commands/set_webview_size.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/webview/autogenerated/commands/set_webview_zoom.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/webview/autogenerated/commands/webview_close.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/webview/autogenerated/commands/webview_hide.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/webview/autogenerated/commands/webview_position.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/webview/autogenerated/commands/webview_show.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/webview/autogenerated/commands/webview_size.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/webview/autogenerated/default.toml"]