["/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/tray/autogenerated/commands/get_by_id.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/tray/autogenerated/commands/new.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/tray/autogenerated/commands/remove_by_id.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/tray/autogenerated/commands/set_icon.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/tray/autogenerated/commands/set_icon_as_template.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/tray/autogenerated/commands/set_menu.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/tray/autogenerated/commands/set_show_menu_on_left_click.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/tray/autogenerated/commands/set_temp_dir_path.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/tray/autogenerated/commands/set_title.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/tray/autogenerated/commands/set_tooltip.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/tray/autogenerated/commands/set_visible.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/tray/autogenerated/default.toml"]