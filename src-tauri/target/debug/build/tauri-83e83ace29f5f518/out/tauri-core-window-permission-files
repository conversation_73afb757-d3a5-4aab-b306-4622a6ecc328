["/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/available_monitors.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/center.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/close.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/create.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/current_monitor.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/cursor_position.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/destroy.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/get_all_windows.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/hide.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/inner_position.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/inner_size.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/internal_toggle_maximize.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/is_always_on_top.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/is_closable.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/is_decorated.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/is_enabled.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/is_focused.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/is_fullscreen.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/is_maximizable.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/is_maximized.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/is_minimizable.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/is_minimized.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/is_resizable.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/is_visible.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/maximize.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/minimize.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/monitor_from_point.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/outer_position.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/outer_size.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/primary_monitor.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/request_user_attention.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/scale_factor.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/set_always_on_bottom.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/set_always_on_top.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/set_background_color.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/set_badge_count.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/set_badge_label.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/set_closable.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/set_content_protected.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/set_cursor_grab.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/set_cursor_icon.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/set_cursor_position.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/set_cursor_visible.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/set_decorations.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/set_effects.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/set_enabled.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/set_focus.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/set_fullscreen.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/set_icon.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/set_ignore_cursor_events.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/set_max_size.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/set_maximizable.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/set_min_size.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/set_minimizable.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/set_overlay_icon.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/set_position.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/set_progress_bar.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/set_resizable.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/set_shadow.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/set_size.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/set_size_constraints.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/set_skip_taskbar.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/set_theme.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/set_title.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/set_title_bar_style.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/set_visible_on_all_workspaces.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/show.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/start_dragging.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/start_resize_dragging.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/theme.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/title.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/toggle_maximize.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/unmaximize.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/commands/unminimize.toml", "/Users/<USER>/Documents/augment-projects/prepwerks-app/src-tauri/target/debug/build/tauri-83e83ace29f5f518/out/permissions/window/autogenerated/default.toml"]