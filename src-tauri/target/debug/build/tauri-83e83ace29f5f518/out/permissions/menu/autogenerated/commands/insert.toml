# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-insert"
description = "Enables the insert command without any pre-configured scope."
commands.allow = ["insert"]

[[permission]]
identifier = "deny-insert"
description = "Denies the insert command without any pre-configured scope."
commands.deny = ["insert"]
