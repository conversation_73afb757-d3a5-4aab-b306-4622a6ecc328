# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-set-decorations"
description = "Enables the set_decorations command without any pre-configured scope."
commands.allow = ["set_decorations"]

[[permission]]
identifier = "deny-set-decorations"
description = "Denies the set_decorations command without any pre-configured scope."
commands.deny = ["set_decorations"]
