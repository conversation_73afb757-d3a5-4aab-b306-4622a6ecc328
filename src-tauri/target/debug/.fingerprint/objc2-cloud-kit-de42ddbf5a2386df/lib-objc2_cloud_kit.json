{"rustc": 15497389221046826682, "features": "[\"CKContainer\", \"CKRecord\", \"CKShare\", \"CKShareMetadata\", \"bitflags\"]", "declared_features": "[\"CKAcceptSharesOperation\", \"CKAllowedSharingOptions\", \"CKAsset\", \"CKContainer\", \"CKDatabase\", \"CKDatabaseOperation\", \"CKDefines\", \"CKDiscoverAllUserIdentitiesOperation\", \"CKDiscoverUserIdentitiesOperation\", \"CKError\", \"CKFetchDatabaseChangesOperation\", \"CKFetchNotificationChangesOperation\", \"CKFetchRecordChangesOperation\", \"CKFetchRecordZoneChangesOperation\", \"CKFetchRecordZonesOperation\", \"CKFetchRecordsOperation\", \"CKFetchShareMetadataOperation\", \"CKFetchShareParticipantsOperation\", \"CKFetchSubscriptionsOperation\", \"CKFetchWebAuthTokenOperation\", \"CKLocationSortDescriptor\", \"CKMarkNotificationsReadOperation\", \"CKModifyBadgeOperation\", \"CKModifyRecordZonesOperation\", \"CKModifyRecordsOperation\", \"CKModifySubscriptionsOperation\", \"CKNotification\", \"CKOperation\", \"CKOperationGroup\", \"CKQuery\", \"CKQueryOperation\", \"CKRecord\", \"CKRecordID\", \"CKRecordZone\", \"CKRecordZoneID\", \"CKReference\", \"CKServerChangeToken\", \"CKShare\", \"CKShareMetadata\", \"CKShareParticipant\", \"CKSubscription\", \"CKSyncEngine\", \"CKSyncEngineConfiguration\", \"CKSyncEngineEvent\", \"CKSyncEngineRecordZoneChangeBatch\", \"CKSyncEngineState\", \"CKSystemSharingUIObserver\", \"CKUserIdentity\", \"CKUserIdentityLookupInfo\", \"NSItemProvider_CKSharingSupport\", \"alloc\", \"bitflags\", \"block2\", \"default\", \"objc2-core-location\", \"std\"]", "target": 15282355710264569945, "profile": 8196097686603091492, "path": 9193311391607464503, "deps": [[1386409696764982933, "objc2", false, 9814518012932224191], [7896293946984509699, "bitflags", false, 15520860525974684933], [9859211262912517217, "objc2_foundation", false, 1849931250936590037]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/objc2-cloud-kit-de42ddbf5a2386df/dep-lib-objc2_cloud_kit", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}