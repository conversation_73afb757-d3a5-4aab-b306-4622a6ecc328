{"rustc": 15497389221046826682, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 5408242616063297496, "profile": 3033921117576893, "path": 785896692879233726, "deps": [[2671782512663819132, "tauri_utils", false, 3573798992185185124], [10806952569398136823, "tauri_build", false, 18414341043068198483], [13077543566650298139, "heck", false, 12709249746560903728], [17155886227862585100, "glob", false, 6621341887591121529]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-7162bd41d7b5ceb7/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}