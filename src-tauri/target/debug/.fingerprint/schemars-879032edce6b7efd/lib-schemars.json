{"rustc": 15497389221046826682, "features": "[\"default\", \"derive\", \"indexmap\", \"preserve_order\", \"schemars_derive\", \"url\", \"uuid1\"]", "declared_features": "[\"arrayvec\", \"arrayvec05\", \"arrayvec07\", \"bigdecimal\", \"bigdecimal03\", \"bigdecimal04\", \"bytes\", \"chrono\", \"default\", \"derive\", \"derive_json_schema\", \"either\", \"enumset\", \"impl_json_schema\", \"indexmap\", \"indexmap1\", \"indexmap2\", \"preserve_order\", \"raw_value\", \"rust_decimal\", \"schemars_derive\", \"semver\", \"smallvec\", \"smol_str\", \"ui_test\", \"url\", \"uuid\", \"uuid08\", \"uuid1\"]", "target": 11155677158530064643, "profile": 3033921117576893, "path": 10393411646586709817, "deps": [[3150220818285335163, "url", false, 8775062253960917464], [6913375703034175521, "build_script_build", false, 9719917670339188014], [8319709847752024821, "uuid1", false, 10882246475171977879], [9122563107207267705, "dyn_clone", false, 11068019916609670845], [9689903380558560274, "serde", false, 4427382413997045785], [14923790796823607459, "indexmap", false, 16066508650446204660], [15367738274754116744, "serde_json", false, 8734402582904541498], [16071897500792579091, "schemars_derive", false, 6330971061344687664]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/schemars-879032edce6b7efd/dep-lib-schemars", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}