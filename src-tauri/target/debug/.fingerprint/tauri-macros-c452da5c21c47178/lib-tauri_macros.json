{"rustc": 15497389221046826682, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 3033921117576893, "path": 6275975848254793694, "deps": [[2671782512663819132, "tauri_utils", false, 16120369952886275129], [3060637413840920116, "proc_macro2", false, 11520194141832687593], [4974441333307933176, "syn", false, 3285790643821001316], [13077543566650298139, "heck", false, 17539410851212178363], [14455244907590647360, "tauri_codegen", false, 12445341031477462130], [17990358020177143287, "quote", false, 12146113348399126031]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-macros-c452da5c21c47178/dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}