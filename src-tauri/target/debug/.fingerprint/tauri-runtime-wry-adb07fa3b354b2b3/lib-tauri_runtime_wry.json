{"rustc": 15497389221046826682, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 5347358027863023418, "path": 15053182754438051212, "deps": [[1386409696764982933, "objc2", false, 9814518012932224191], [2671782512663819132, "tauri_utils", false, 16639227477773203622], [3150220818285335163, "url", false, 8918879565154682729], [4143744114649553716, "raw_window_handle", false, 196020354320396666], [5986029879202738730, "log", false, 13517631300886753083], [6089812615193535349, "tauri_runtime", false, 5835347427170019902], [8826339825490770380, "tao", false, 6948539168072262837], [9010263965687315507, "http", false, 8361839584996066817], [9141053277961803901, "wry", false, 16899178479937285928], [9859211262912517217, "objc2_foundation", false, 1849931250936590037], [10575598148575346675, "objc2_app_kit", false, 10239512390440847286], [11599800339996261026, "build_script_build", false, 4389665471220558052]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-runtime-wry-adb07fa3b354b2b3/dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}