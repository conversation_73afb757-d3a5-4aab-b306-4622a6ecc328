{"rustc": 15497389221046826682, "features": "[\"WKDownload\", \"WKDownloadDelegate\", \"WKFrameInfo\", \"WKHTTPCookieStore\", \"WKNavigation\", \"WKNavigationAction\", \"WKNavigationDelegate\", \"WKNavigationResponse\", \"WKOpenPanelParameters\", \"WKPreferences\", \"WKScriptMessage\", \"WKScriptMessageHandler\", \"WKSecurityOrigin\", \"WKUIDelegate\", \"WKURLSchemeHandler\", \"WKURLSchemeTask\", \"WKUserContentController\", \"WKUserScript\", \"WKWebView\", \"WKWebViewConfiguration\", \"WKWebpagePreferences\", \"WKWebsiteDataStore\", \"alloc\", \"bitflags\", \"block2\", \"objc2-app-kit\", \"objc2-core-foundation\", \"std\"]", "declared_features": "[\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>View\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", \"DOMB<PERSON>b\", \"DOMCDATASection\", \"DOMCSS\", \"DOMCSSCharsetRule\", \"DOMCSSFontFaceRule\", \"DOMCSSImportRule\", \"DOMCSSMediaRule\", \"DOMCSSPageRule\", \"DOMCSSPrimitiveValue\", \"DOMCSSRule\", \"DOMCSSRuleList\", \"DOMCSSStyleDeclaration\", \"DOMCSSStyleRule\", \"DOMCSSStyleSheet\", \"DOMCSSUnknownRule\", \"DOMCSSValue\", \"DOMCSSValueList\", \"DOMCharacterData\", \"DOMComment\", \"DOMCore\", \"DOM<PERSON>ounter\", \"DOMDocument\", \"DOMDocumentFragment\", \"DOMDocumentType\", \"DOMElement\", \"DOMEntity\", \"DOMEntityReference\", \"DOMEvent\", \"DOMEventException\", \"<PERSON>OMEvent<PERSON>ist<PERSON>\", \"<PERSON><PERSON><PERSON>vent<PERSON>ar<PERSON>\", \"<PERSON><PERSON>E<PERSON>s\", \"<PERSON><PERSON><PERSON>x<PERSON>\", \"<PERSON><PERSON><PERSON>xtensions\", \"DOMFile\", \"<PERSON>OM<PERSON><PERSON>List\", \"<PERSON><PERSON>HT<PERSON>\", \"DOM<PERSON>MLAnchorElement\", \"DOMHTMLAppletElement\", \"DOMHTMLAreaElement\", \"DOMHTMLBRElement\", \"DOMHTMLBaseElement\", \"DOMHTMLBaseFontElement\", \"DOMHTMLBodyElement\", \"DOMHTMLButtonElement\", \"DOMHTMLCollection\", \"DOMHTMLDListElement\", \"DOMHTMLDirectoryElement\", \"DOMHTMLDivElement\", \"DOMHTMLDocument\", \"DOMHTMLElement\", \"DOMHTMLEmbedElement\", \"DOMHTMLFieldSetElement\", \"DOMHTMLFontElement\", \"DOMHTMLFormElement\", \"DOMHTMLFrameElement\", \"DOMHTMLFrameSetElement\", \"DOMHTMLHRElement\", \"DOMHTMLHeadElement\", \"DOMHTMLHeadingElement\", \"DOMHTMLHtmlElement\", \"DOMHTMLIFrameElement\", \"DOMHTMLImageElement\", \"DOMHTMLInputElement\", \"DOMHTMLLIElement\", \"DOMHTMLLabelElement\", \"DOMHTMLLegendElement\", \"DOMHTMLLinkElement\", \"DOMHTMLMapElement\", \"DOMHTMLMarqueeElement\", \"DOMHTMLMenuElement\", \"DOMHTMLMetaElement\", \"DOMHTMLModElement\", \"DOMHTMLOListElement\", \"DOMHTMLObjectElement\", \"DOMHTMLOptGroupElement\", \"DOMHTMLOptionElement\", \"DOMHTMLOptionsCollection\", \"DOMHTMLParagraphElement\", \"DOMHTMLParamElement\", \"DOMHTMLPreElement\", \"DOMHTMLQuoteElement\", \"DOMHTMLScriptElement\", \"DOMHTMLSelectElement\", \"DOMHTMLStyleElement\", \"DOMHTMLTableCaptionElement\", \"DOMHTMLTableCellElement\", \"DOMHTMLTableColElement\", \"DOMHTMLTableElement\", \"DOMHTMLTableRowElement\", \"DOMHTMLTableSectionElement\", \"DOMHTMLTextAreaElement\", \"DOMHTMLTitleElement\", \"DOMHTMLUListElement\", \"DOMImplementation\", \"DOMKeyboardEvent\", \"DOMMediaList\", \"DOMMouseEvent\", \"DOMMutationEvent\", \"DOMNamedNodeMap\", \"DOMNode\", \"DOMNodeFilter\", \"DOMNodeIterator\", \"DOMNodeList\", \"DOMObject\", \"DOMOverflowEvent\", \"DOMProcessingInstruction\", \"DOMProgressEvent\", \"DOMRGBColor\", \"DOMRange\", \"DOMRangeException\", \"DOMRanges\", \"DOMRect\", \"DOMStyleSheet\", \"DOMStyleSheetList\", \"DOMStylesheets\", \"DOMText\", \"DOMTraversal\", \"DOMTreeWalker\", \"DOMUIEvent\", \"DOMViews\", \"DOMWheelEvent\", \"DOMXPath\", \"DOMXPathException\", \"DOMXPathExpression\", \"DOMXPathNSResolver\", \"DOMXPathResult\", \"NSAttributedString\", \"WKBackForwardList\", \"WKBackForwardListItem\", \"WKContentRuleList\", \"WKContentRuleListStore\", \"WKContentWorld\", \"WKContextMenuElementInfo\", \"WKDataDetectorTypes\", \"WKDownload\", \"WKDownloadDelegate\", \"WKError\", \"WKFindConfiguration\", \"WKFindResult\", \"WKFoundation\", \"WKFrameInfo\", \"WKHTTPCookieStore\", \"WKNavigation\", \"WKNavigationAction\", \"WKNavigationDelegate\", \"WKNavigationResponse\", \"WKOpenPanelParameters\", \"WKPDFConfiguration\", \"WKPreferences\", \"WKPreviewActionItem\", \"WKPreviewActionItemIdentifiers\", \"WKPreviewElementInfo\", \"WKProcessPool\", \"WKScriptMessage\", \"WKScriptMessageHandler\", \"WKScriptMessageHandlerWithReply\", \"WKSecurityOrigin\", \"WKSnapshotConfiguration\", \"WKUIDelegate\", \"WKURLSchemeHandler\", \"WKURLSchemeTask\", \"WKUserContentController\", \"WKUserScript\", \"WKWebExtension\", \"WKWebExtensionAction\", \"WKWebExtensionCommand\", \"WKWebExtensionContext\", \"WKWebExtensionController\", \"WKWebExtensionControllerConfiguration\", \"WKWebExtensionControllerDelegate\", \"WKWebExtensionDataRecord\", \"WKWebExtensionDataType\", \"WKWebExtensionMatchPattern\", \"WKWebExtensionMessagePort\", \"WKWebExtensionPermission\", \"WKWebExtensionTab\", \"WKWebExtensionTabConfiguration\", \"WKWebExtensionWindow\", \"WKWebExtensionWindowConfiguration\", \"WKWebView\", \"WKWebViewConfiguration\", \"WKWebpagePreferences\", \"WKWebsiteDataRecord\", \"WKWebsiteDataStore\", \"WKWindowFeatures\", \"WebArchive\", \"WebBackForwardList\", \"WebDOMOperations\", \"WebDataSource\", \"WebDocument\", \"WebDownload\", \"WebEditingDelegate\", \"WebFrame\", \"WebFrameLoadDelegate\", \"WebFrameView\", \"WebHistory\", \"WebHistoryItem\", \"WebKitAvailability\", \"WebKitErrors\", \"WebKitLegacy\", \"WebPlugin\", \"WebPluginContainer\", \"WebPluginViewFactory\", \"WebPolicyDelegate\", \"WebPreferences\", \"WebResource\", \"WebResourceLoadDelegate\", \"WebScriptObject\", \"WebUIDelegate\", \"WebView\", \"alloc\", \"bitflags\", \"block2\", \"default\", \"objc2-app-kit\", \"objc2-core-foundation\", \"objc2-javascript-core\", \"objc2-security\", \"std\"]", "target": 10360698204467479185, "profile": 8196097686603091492, "path": 3924264511682424344, "deps": [[309970253587158206, "block2", false, 8936140624945061511], [1386409696764982933, "objc2", false, 9814518012932224191], [7896293946984509699, "bitflags", false, 15520860525974684933], [9859211262912517217, "objc2_foundation", false, 1849931250936590037], [10378802769730441691, "objc2_core_foundation", false, 11028571983764007357], [10575598148575346675, "objc2_app_kit", false, 10239512390440847286]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/objc2-web-kit-c8a3b98f83088a7b/dep-lib-objc2_web_kit", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}