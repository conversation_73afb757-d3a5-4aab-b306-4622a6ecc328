{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[]", "target": 2684992013079232362, "profile": 5347358027863023418, "path": 4848209968280500925, "deps": [[1218881066841546592, "symphonia_core", false, 4200758284128645610], [5986029879202738730, "log", false, 13517631300886753083], [14564311161534545801, "encoding_rs", false, 14897020818206116180], [17917672826516349275, "lazy_static", false, 13077351185285163301]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/symphonia-metadata-a592df331183c7f2/dep-lib-symphonia_metadata", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}