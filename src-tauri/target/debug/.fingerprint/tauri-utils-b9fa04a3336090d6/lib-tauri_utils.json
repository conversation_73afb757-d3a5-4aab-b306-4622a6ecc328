{"rustc": 15497389221046826682, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 5347358027863023418, "path": 9517768389927292602, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 13082427841578684426], [3150220818285335163, "url", false, 8918879565154682729], [3191507132440681679, "serde_untagged", false, 17704851324127287480], [4071963112282141418, "serde_with", false, 16418617696052750337], [4899080583175475170, "semver", false, 18054490425553439165], [5986029879202738730, "log", false, 13517631300886753083], [6606131838865521726, "ctor", false, 2936259950701660017], [7170110829644101142, "json_patch", false, 4709440421053244082], [8319709847752024821, "uuid", false, 14810063812620333486], [9010263965687315507, "http", false, 8361839584996066817], [9451456094439810778, "regex", false, 4299037736249060348], [9556762810601084293, "brotli", false, 9576487239323992329], [9689903380558560274, "serde", false, 18187077930933628250], [10806645703491011684, "thiserror", false, 7190982899289874411], [11989259058781683633, "dunce", false, 11512646038938628873], [13625485746686963219, "anyhow", false, 4669025522612569633], [15367738274754116744, "serde_json", false, 18442862793593489669], [15609422047640926750, "toml", false, 17480237124432338588], [15622660310229662834, "walkdir", false, 8633821404424625841], [15932120279885307830, "memchr", false, 16067265597352771720], [17146114186171651583, "infer", false, 14403190154178105651], [17155886227862585100, "glob", false, 562982324261335501], [17186037756130803222, "phf", false, 9398432571452948236]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-utils-b9fa04a3336090d6/dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}