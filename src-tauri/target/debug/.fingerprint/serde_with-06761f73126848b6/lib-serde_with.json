{"rustc": 15497389221046826682, "features": "[\"alloc\", \"default\", \"macros\", \"std\"]", "declared_features": "[\"alloc\", \"base64\", \"chrono\", \"chrono_0_4\", \"default\", \"guide\", \"hashbrown_0_14\", \"hashbrown_0_15\", \"hex\", \"indexmap\", \"indexmap_1\", \"indexmap_2\", \"json\", \"macros\", \"schemars_0_8\", \"schemars_0_9\", \"schemars_1\", \"std\", \"time_0_3\"]", "target": 10448421281463538527, "profile": 3280471839685724059, "path": 5163665298536033334, "deps": [[7026957619838884710, "serde_with_macros", false, 16812146850745461531], [9689903380558560274, "serde", false, 18187077930933628250], [16257276029081467297, "serde_derive", false, 5008627168916031761]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/serde_with-06761f73126848b6/dep-lib-serde_with", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}