{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 5347358027863023418, "path": 17411785022043046859, "deps": [[2671782512663819132, "tauri_utils", false, 16639227477773203622], [3150220818285335163, "url", false, 8918879565154682729], [4143744114649553716, "raw_window_handle", false, 196020354320396666], [6089812615193535349, "build_script_build", false, 3967666155607028013], [7606335748176206944, "dpi", false, 5501171416595881008], [9010263965687315507, "http", false, 8361839584996066817], [9689903380558560274, "serde", false, 18187077930933628250], [10806645703491011684, "thiserror", false, 7190982899289874411], [15367738274754116744, "serde_json", false, 18442862793593489669], [16727543399706004146, "cookie", false, 532324751408635554]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-runtime-a9f3f9bc7ffd7973/dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}