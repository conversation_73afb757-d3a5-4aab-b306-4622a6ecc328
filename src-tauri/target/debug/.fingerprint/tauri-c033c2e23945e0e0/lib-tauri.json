{"rustc": 15497389221046826682, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 5347358027863023418, "path": 4232404182795128005, "deps": [[40386456601120721, "percent_encoding", false, 2499288624308541304], [1200537532907108615, "url<PERSON><PERSON>n", false, 13082427841578684426], [1386409696764982933, "objc2", false, 9814518012932224191], [2616743947975331138, "plist", false, 12733863076323136542], [2671782512663819132, "tauri_utils", false, 16639227477773203622], [3150220818285335163, "url", false, 8918879565154682729], [3331586631144870129, "getrandom", false, 10653083903324954813], [4143744114649553716, "raw_window_handle", false, 196020354320396666], [4494683389616423722, "muda", false, 11826615692549391866], [4919829919303820331, "serialize_to_javascript", false, 1215643407353858792], [5986029879202738730, "log", false, 13517631300886753083], [6089812615193535349, "tauri_runtime", false, 5835347427170019902], [7573826311589115053, "tauri_macros", false, 1709801381595621091], [8589231650440095114, "embed_plist", false, 17288946656552610050], [9010263965687315507, "http", false, 8361839584996066817], [9538054652646069845, "tokio", false, 3183010730372886932], [9689903380558560274, "serde", false, 18187077930933628250], [9859211262912517217, "objc2_foundation", false, 1849931250936590037], [10229185211513642314, "mime", false, 4679492081772102237], [10575598148575346675, "objc2_app_kit", false, 10239512390440847286], [10806645703491011684, "thiserror", false, 7190982899289874411], [11599800339996261026, "tauri_runtime_wry", false, 4801187255614886172], [11989259058781683633, "dunce", false, 11512646038938628873], [12565293087094287914, "window_vibrancy", false, 6155318892689613688], [12986574360607194341, "serde_repr", false, 14905493724689521343], [13077543566650298139, "heck", false, 17539410851212178363], [13625485746686963219, "anyhow", false, 4669025522612569633], [14039947826026167952, "build_script_build", false, 1450911761014279048], [15367738274754116744, "serde_json", false, 18442862793593489669], [16928111194414003569, "dirs", false, 6945827186464584520], [17155886227862585100, "glob", false, 562982324261335501]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-c033c2e23945e0e0/dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}