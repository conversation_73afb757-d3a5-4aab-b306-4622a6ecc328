{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[]", "target": 2977321560937920362, "profile": 5347358027863023418, "path": 14168269351000833895, "deps": [[4972584477725338812, "build_script_build", false, 13182972974442284023], [5986029879202738730, "log", false, 13517631300886753083], [9451456094439810778, "regex", false, 4299037736249060348], [9538054652646069845, "tokio", false, 3183010730372886932], [9689903380558560274, "serde", false, 18187077930933628250], [10806645703491011684, "thiserror", false, 7190982899289874411], [11337703028400419576, "os_pipe", false, 5318441142162770361], [14039947826026167952, "tauri", false, 3012016218048595460], [14564311161534545801, "encoding_rs", false, 14897020818206116180], [15367738274754116744, "serde_json", false, 18442862793593489669], [16192041687293812804, "open", false, 3676066404625684495], [17642783533582771402, "shared_child", false, 5637453165078781960]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-plugin-shell-d8c190dbc9f9743b/dep-lib-tauri_plugin_shell", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}