{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[]", "target": 7744028988904646781, "profile": 5347358027863023418, "path": 13917357101279964169, "deps": [[1218881066841546592, "symphonia_core", false, 4200758284128645610], [7059103048047618386, "symphonia_metadata", false, 10013913484630103533]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/symphonia-utils-xiph-ed61b8147594bea1/dep-lib-symphonia_utils_xiph", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}