{"rustc": 15497389221046826682, "features": "[\"default\"]", "declared_features": "[\"default\", \"opt-simd\", \"opt-simd-avx\", \"opt-simd-neon\", \"opt-simd-sse\", \"rustfft\"]", "target": 18060829941030025544, "profile": 5347358027863023418, "path": 17772300909108423535, "deps": [[5986029879202738730, "log", false, 13517631300886753083], [6511429716036861196, "bytemuck", false, 17487993247766765520], [10435729446543529114, "bitflags", false, 11090415386117837886], [13847662864258534762, "arrayvec", false, 7808185607673619373], [17917672826516349275, "lazy_static", false, 13077351185285163301]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/symphonia-core-08190fdb98b503a3/dep-lib-symphonia_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}