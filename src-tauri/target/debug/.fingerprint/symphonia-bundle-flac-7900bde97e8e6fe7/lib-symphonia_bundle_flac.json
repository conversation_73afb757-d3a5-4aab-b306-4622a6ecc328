{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[]", "target": 18426575973585550006, "profile": 5347358027863023418, "path": 12367062563375723809, "deps": [[1218881066841546592, "symphonia_core", false, 4200758284128645610], [2423495850963981082, "symphonia_utils_xiph", false, 1420192249692117310], [5986029879202738730, "log", false, 13517631300886753083], [7059103048047618386, "symphonia_metadata", false, 10013913484630103533]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/symphonia-bundle-flac-7900bde97e8e6fe7/dep-lib-symphonia_bundle_flac", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}