{"rustc": 15497389221046826682, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 3033921117576893, "path": 9528141379963423326, "deps": [[2671782512663819132, "tauri_utils", false, 16120369952886275129], [4899080583175475170, "semver", false, 13158264630602531549], [6913375703034175521, "schemars", false, 19483541972508943], [7170110829644101142, "json_patch", false, 8640550452910809128], [9689903380558560274, "serde", false, 4427382413997045785], [12714016054753183456, "tauri_winres", false, 7595039948747574333], [13077543566650298139, "heck", false, 17539410851212178363], [13475171727366188400, "cargo_toml", false, 2434133956221025737], [13625485746686963219, "anyhow", false, 4669025522612569633], [15367738274754116744, "serde_json", false, 8734402582904541498], [15609422047640926750, "toml", false, 4675775598779157408], [15622660310229662834, "walkdir", false, 8633821404424625841], [16928111194414003569, "dirs", false, 6945827186464584520], [17155886227862585100, "glob", false, 562982324261335501]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-build-66fa11e727c6faf9/dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}