{"rustc": 15497389221046826682, "features": "[\"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"html-manipulation\", \"proc-macro2\", \"quote\", \"resources\", \"schema\", \"schemars\", \"swift-rs\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 3033921117576893, "path": 9517768389927292602, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 14005766192405083019], [3060637413840920116, "proc_macro2", false, 11520194141832687593], [3150220818285335163, "url", false, 8775062253960917464], [3191507132440681679, "serde_untagged", false, 16682431368608247375], [4071963112282141418, "serde_with", false, 14481873452354838911], [4899080583175475170, "semver", false, 13158264630602531549], [5986029879202738730, "log", false, 12707097318041091084], [6606131838865521726, "ctor", false, 2936259950701660017], [6913375703034175521, "schemars", false, 19483541972508943], [7170110829644101142, "json_patch", false, 8640550452910809128], [8319709847752024821, "uuid", false, 10882246475171977879], [9010263965687315507, "http", false, 8361839584996066817], [9451456094439810778, "regex", false, 4299037736249060348], [9556762810601084293, "brotli", false, 9576487239323992329], [9689903380558560274, "serde", false, 4427382413997045785], [10806645703491011684, "thiserror", false, 7190982899289874411], [11655476559277113544, "cargo_metadata", false, 13236287890157596243], [11989259058781683633, "dunce", false, 11512646038938628873], [13625485746686963219, "anyhow", false, 4669025522612569633], [14232843520438415263, "html5ever", false, 6082235462206453029], [14885200901422974105, "swift_rs", false, 18395442790174831631], [15088007382495681292, "kuchiki", false, 6263975170336328146], [15367738274754116744, "serde_json", false, 8734402582904541498], [15609422047640926750, "toml", false, 4675775598779157408], [15622660310229662834, "walkdir", false, 8633821404424625841], [15932120279885307830, "memchr", false, 16067265597352771720], [17146114186171651583, "infer", false, 18053038615962376895], [17155886227862585100, "glob", false, 562982324261335501], [17186037756130803222, "phf", false, 16826920028660513731], [17990358020177143287, "quote", false, 12146113348399126031]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-utils-910376c96965e353/dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}