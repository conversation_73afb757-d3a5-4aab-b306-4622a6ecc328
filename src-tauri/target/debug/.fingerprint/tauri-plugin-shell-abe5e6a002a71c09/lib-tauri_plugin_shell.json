{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[]", "target": 2977321560937920362, "profile": 8276155916380437441, "path": 14168269351000833895, "deps": [[4972584477725338812, "build_script_build", false, 10335399332994978731], [5986029879202738730, "log", false, 7456642417201623819], [9451456094439810778, "regex", false, 16660222130784957986], [9538054652646069845, "tokio", false, 10620762346404766619], [9689903380558560274, "serde", false, 13025995231774814534], [10806645703491011684, "thiserror", false, 17136075049722905815], [11337703028400419576, "os_pipe", false, 15621426260049818228], [14039947826026167952, "tauri", false, 13605982026788626993], [14564311161534545801, "encoding_rs", false, 16801086342953762384], [15367738274754116744, "serde_json", false, 18360736782743864389], [16192041687293812804, "open", false, 6198824299977315377], [17642783533582771402, "shared_child", false, 12147981984804424545]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-plugin-shell-abe5e6a002a71c09/dep-lib-tauri_plugin_shell", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}