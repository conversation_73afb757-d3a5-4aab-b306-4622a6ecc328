{"rustc": 15497389221046826682, "features": "[\"CFArray\", \"CFBase\", \"CFCGTypes\", \"CFData\", \"CFDate\", \"CFDictionary\", \"CFRunLoop\", \"CFString\", \"CFURL\", \"CFUserNotification\", \"alloc\", \"bitflags\", \"objc2\", \"std\"]", "declared_features": "[\"CFArray\", \"CFAttributedString\", \"CFAvailability\", \"CFBag\", \"CFBase\", \"CFBinaryHeap\", \"CFBitVector\", \"CFBundle\", \"CFByteOrder\", \"CFCGTypes\", \"CFCalendar\", \"CFCharacterSet\", \"CFData\", \"CFDate\", \"CFDateFormatter\", \"CFDictionary\", \"CFError\", \"CFFileDescriptor\", \"CFFileSecurity\", \"CFLocale\", \"CFMachPort\", \"CFMessagePort\", \"CFNotificationCenter\", \"CFNumber\", \"CFNumberFormatter\", \"CFPlugIn\", \"CFPlugInCOM\", \"CFPreferences\", \"CFPropertyList\", \"CFRunLoop\", \"CFSet\", \"CFSocket\", \"CFStream\", \"CFString\", \"CFStringEncodingExt\", \"CFStringTokenizer\", \"CFTimeZone\", \"CFTree\", \"CFURL\", \"CFURLAccess\", \"CFURLEnumerator\", \"CFUUID\", \"CFUserNotification\", \"CFUtilities\", \"CFXMLNode\", \"CFXMLParser\", \"alloc\", \"bitflags\", \"block2\", \"default\", \"dispatch2\", \"libc\", \"objc2\", \"std\", \"unstable-coerce-pointee\"]", "target": 9250166696766853962, "profile": 8196097686603091492, "path": 3670508345903123738, "deps": [[1386409696764982933, "objc2", false, 9814518012932224191], [7896293946984509699, "bitflags", false, 15520860525974684933]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/objc2-core-foundation-d0128340c2a5da38/dep-lib-objc2_core_foundation", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}