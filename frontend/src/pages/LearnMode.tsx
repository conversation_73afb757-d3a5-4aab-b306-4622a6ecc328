import { useState, useEffect } from 'react';
import { FolderOpen, Play, FileText, Database, X } from 'lucide-react';
import { audioService } from '../services/audioService';
import { ScanProgress, AudioFile, DirectoryInfo } from '../types/audio';

export function LearnMode() {
  const [selectedDirectory, setSelectedDirectory] = useState<string>('');
  const [rekordboxXmlPath, setRekordboxXmlPath] = useState<string>('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [scanProgress, setScanProgress] = useState<ScanProgress | null>(null);
  const [scanResults, setScanResults] = useState<AudioFile[]>([]);
  const [directoryInfo, setDirectoryInfo] = useState<DirectoryInfo | null>(null);
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
  const [error, setError] = useState<string>('');

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (currentSessionId) {
        audioService.cleanupScanSession(currentSessionId);
      }
    };
  }, [currentSessionId]);

  const handleSelectDirectory = async () => {
    try {
      const directory = await audioService.selectDirectory();
      if (directory) {
        setSelectedDirectory(directory);
        setError('');

        // Get directory info
        try {
          const info = await audioService.getDirectoryInfo(directory);
          setDirectoryInfo(info);
        } catch (err) {
          console.error('Failed to get directory info:', err);
        }
      }
    } catch (err) {
      setError('Failed to select directory');
      console.error('Directory selection error:', err);
    }
  };

  const handleSelectRekordboxXml = async () => {
    try {
      const file = await audioService.selectDirectory(); // This would be modified to select files
      if (file) {
        setRekordboxXmlPath(file);
      }
    } catch (err) {
      console.error('Failed to select Rekordbox XML:', err);
    }
  };

  const handleStartAnalysis = async () => {
    if (!selectedDirectory) return;

    try {
      setIsAnalyzing(true);
      setError('');
      setScanProgress(null);
      setScanResults([]);

      const sessionId = await audioService.startDirectoryScan(selectedDirectory);
      setCurrentSessionId(sessionId);

      // Monitor progress
      audioService.monitorScanProgress(
        sessionId,
        (progress) => {
          setScanProgress(progress);
        },
        (results) => {
          setScanResults(results);
          setIsAnalyzing(false);
          setScanProgress(prev => prev ? { ...prev, percentage: 100, status: 'Analysis complete!' } : null);
        },
        (error) => {
          setError(error);
          setIsAnalyzing(false);
        }
      );
    } catch (err) {
      setError(`Failed to start analysis: ${err}`);
      setIsAnalyzing(false);
    }
  };

  const handleCancelAnalysis = async () => {
    if (currentSessionId) {
      try {
        await audioService.cancelScan(currentSessionId);
        setIsAnalyzing(false);
        setScanProgress(null);
        setCurrentSessionId(null);
      } catch (err) {
        console.error('Failed to cancel scan:', err);
      }
    }
  };

  return (
    <div className="page">
      <div className="page-header">
        <h1 className="page-title">Learn Mode</h1>
        <p className="page-description">
          Analyze your existing music library to learn organization patterns and preferences.
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Directory Selection */}
        <div className="card">
          <div className="card-header">
            <h2 className="card-title">
              <FolderOpen size={20} className="inline-block mr-2" />
              Music Library Selection
            </h2>
          </div>

          <div className="form-group">
            <label className="form-label">Select Music Directory</label>
            <div className="flex gap-2">
              <input
                type="text"
                className="form-input flex-1"
                value={selectedDirectory}
                placeholder="Choose your music library folder..."
                readOnly
              />
              <button
                className="btn btn-secondary"
                onClick={handleSelectDirectory}
              >
                Browse
              </button>
            </div>
          </div>

          <div className="form-group">
            <label className="form-label">Rekordbox XML (Optional)</label>
            <div className="flex gap-2">
              <input
                type="text"
                className="form-input flex-1"
                value={rekordboxXmlPath}
                placeholder="Select rekordbox.xml file..."
                readOnly
              />
              <button
                className="btn btn-secondary"
                onClick={handleSelectRekordboxXml}
              >
                <Database size={16} />
                Browse
              </button>
            </div>
          </div>

          {directoryInfo && (
            <div className="bg-gray-700 rounded p-3 mb-4">
              <h4 className="font-medium mb-2">Directory Information</h4>
              <div className="text-sm space-y-1">
                <div>Total files: {directoryInfo.total_files}</div>
                <div>Audio files: {directoryInfo.audio_files}</div>
                <div>Total size: {(directoryInfo.total_size / (1024 * 1024)).toFixed(1)} MB</div>
              </div>
            </div>
          )}

          {error && (
            <div className="bg-red-500/20 border border-red-500 rounded p-3 mb-4">
              <div className="text-red-400 text-sm">{error}</div>
            </div>
          )}

          <div className="flex gap-2">
            <button
              className="btn btn-primary flex-1"
              onClick={handleStartAnalysis}
              disabled={!selectedDirectory || isAnalyzing}
            >
              <Play size={16} />
              {isAnalyzing ? 'Analyzing...' : 'Start Analysis'}
            </button>

            {isAnalyzing && (
              <button
                className="btn btn-secondary"
                onClick={handleCancelAnalysis}
              >
                <X size={16} />
                Cancel
              </button>
            )}
          </div>
        </div>

        {/* Analysis Progress */}
        <div className="card">
          <div className="card-header">
            <h2 className="card-title">
              <FileText size={20} className="inline-block mr-2" />
              Analysis Progress
            </h2>
          </div>

          {scanProgress && (
            <div className="space-y-4">
              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span>Overall Progress</span>
                  <span>{Math.round(scanProgress.percentage)}%</span>
                </div>
                <div className="progress-bar">
                  <div
                    className="progress-fill"
                    style={{ width: `${scanProgress.percentage}%` }}
                  />
                </div>
              </div>

              <div className="text-sm text-gray-400">
                <p>Status: {scanProgress.status}</p>
                <p>Files processed: {scanProgress.files_processed} / {scanProgress.total_files}</p>
                <p>Current path: {scanProgress.current_path}</p>
                {scanProgress.total_bytes > 0 && (
                  <p>Data processed: {(scanProgress.bytes_processed / (1024 * 1024)).toFixed(1)} MB / {(scanProgress.total_bytes / (1024 * 1024)).toFixed(1)} MB</p>
                )}
              </div>
            </div>
          )}

          {!scanProgress && !isAnalyzing && (
            <p className="text-gray-400">
              Select a music directory and start analysis to see progress here.
            </p>
          )}

          {scanResults.length > 0 && !isAnalyzing && (
            <div className="text-green-400">
              <p>✓ Analysis complete! Found {scanResults.length} audio files.</p>
            </div>
          )}
        </div>
      </div>

      {/* Analysis Results */}
      {scanResults.length > 0 && (
        <div className="card mt-6">
          <div className="card-header">
            <h2 className="card-title">Analysis Results</h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-500">{scanResults.length}</div>
              <div className="text-sm text-gray-400">Tracks Analyzed</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-500">
                {new Set(scanResults.map(f => f.metadata.sub_genre).filter(Boolean)).size}
              </div>
              <div className="text-sm text-gray-400">Sub-genres Identified</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-500">
                {Math.round((scanResults.filter(f => f.metadata.bpm || f.metadata.key).length / scanResults.length) * 100)}%
              </div>
              <div className="text-sm text-gray-400">Files with BPM/Key</div>
            </div>
          </div>

          {/* Sample of analyzed files */}
          <div>
            <h3 className="font-medium mb-3">Sample Files</h3>
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {scanResults.slice(0, 10).map((file, index) => (
                <div key={index} className="bg-gray-700 rounded p-3">
                  <div className="font-medium text-sm">{file.filename}</div>
                  <div className="text-xs text-gray-400 mt-1">
                    {file.metadata.artist && <span>Artist: {file.metadata.artist} • </span>}
                    {file.metadata.bpm && <span>BPM: {file.metadata.bpm} • </span>}
                    {file.metadata.key && <span>Key: {file.metadata.key} • </span>}
                    {file.metadata.sub_genre && <span>Genre: {file.metadata.sub_genre}</span>}
                  </div>
                </div>
              ))}
              {scanResults.length > 10 && (
                <div className="text-center text-sm text-gray-400">
                  ... and {scanResults.length - 10} more files
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
