export interface AudioMetadata {
  title?: string;
  artist?: string;
  album?: string;
  year?: number;
  genre?: string;
  track?: number;
  duration?: number;
  bitrate?: number;
  sample_rate?: number;
  channels?: number;
  format: string;
  file_size: number;
  bpm?: number;
  key?: string;
  comment?: string;
  energy_level?: string;
  sub_genre?: string;
}

export interface SpectralFeatures {
  low_freq_energy: number;
  mid_freq_energy: number;
  high_freq_energy: number;
  spectral_centroid: number;
  spectral_rolloff: number;
}

export interface AudioAnalysis {
  detected_bpm?: number;
  detected_key?: string;
  energy_level?: number;
  has_vocals?: boolean;
  spectral_features?: SpectralFeatures;
}

export interface AudioFile {
  path: string;
  filename: string;
  metadata: AudioMetadata;
  analysis?: AudioAnalysis;
}

export interface DirectoryFilter {
  include_patterns: string[];
  exclude_patterns: string[];
  file_types: string[];
  min_file_size?: number;
  max_file_size?: number;
  max_depth?: number;
  regex_patterns: string[];
}

export interface DirectoryStructure {
  path: string;
  name: string;
  is_directory: boolean;
  children: DirectoryStructure[];
  audio_files: AudioFile[];
  total_files: number;
  total_size: number;
}

export interface ScanProgress {
  current_path: string;
  files_processed: number;
  total_files: number;
  bytes_processed: number;
  total_bytes: number;
  percentage: number;
  status: string;
}

export interface DirectoryInfo {
  path: string;
  total_files: number;
  audio_files: number;
  total_size: number;
  readable: boolean;
}

export const DEFAULT_DIRECTORY_FILTER: DirectoryFilter = {
  include_patterns: ['*'],
  exclude_patterns: [],
  file_types: ['mp3', 'flac', 'wav', 'm4a', 'aac', 'aiff', 'ogg'],
  min_file_size: 1024, // 1KB
  max_file_size: 1024 * 1024 * 1024, // 1GB
  max_depth: undefined,
  regex_patterns: [],
};

export const SUPPORTED_AUDIO_FORMATS = [
  'mp3', 'flac', 'wav', 'm4a', 'aac', 'aiff', 'ogg'
] as const;

export type SupportedAudioFormat = typeof SUPPORTED_AUDIO_FORMATS[number];

export const ENERGY_LEVELS = [
  'O', 'OM', 'M', 'ME', 'E', 'OPEN', 'END'
] as const;

export type EnergyLevel = typeof ENERGY_LEVELS[number];

export const SUB_GENRES = [
  'dark', 'ht', 'jungle', 'liquid', 'tf'
] as const;

export type SubGenre = typeof SUB_GENRES[number];

export interface CommentFieldStructure {
  vocal_info?: string;
  sub_genre?: string;
  description?: string;
}

export function parseCommentField(comment?: string): CommentFieldStructure {
  if (!comment) return {};
  
  const parts = comment.split('. ');
  const result: CommentFieldStructure = {};
  
  if (parts.length >= 1) {
    result.vocal_info = parts[0].replace(/[\[\]]/g, '');
  }
  
  if (parts.length >= 2) {
    result.sub_genre = parts[1].replace(/[\[\]]/g, '');
  }
  
  if (parts.length >= 3) {
    result.description = parts.slice(2).join('. ').replace(/[\[\]]/g, '');
  }
  
  return result;
}

export function formatCommentField(structure: CommentFieldStructure): string {
  const parts: string[] = [];
  
  if (structure.vocal_info) {
    parts.push(`[${structure.vocal_info}]`);
  }
  
  if (structure.sub_genre) {
    parts.push(`[${structure.sub_genre}]`);
  }
  
  if (structure.description) {
    parts.push(`[${structure.description}]`);
  }
  
  return parts.join('. ');
}

export function formatFileSize(bytes: number): string {
  const units = ['B', 'KB', 'MB', 'GB'];
  let size = bytes;
  let unitIndex = 0;
  
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }
  
  return `${size.toFixed(1)} ${units[unitIndex]}`;
}

export function formatDuration(seconds?: number): string {
  if (!seconds) return 'Unknown';
  
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
}

export function extractBpmFromFilename(filename: string): number | undefined {
  const bpmMatch = filename.match(/\[(\d+)\s*[A-Za-z]*\]/);
  return bpmMatch ? parseInt(bpmMatch[1], 10) : undefined;
}

export function extractKeyFromFilename(filename: string): string | undefined {
  const keyMatch = filename.match(/\[\d+\s+([A-Za-z]+(?:maj|min))\]/);
  return keyMatch ? keyMatch[1] : undefined;
}

export function extractEnergyFromFilename(filename: string): string | undefined {
  const energyMatch = filename.match(/\[([OME]+|OPEN|END)\]$/);
  return energyMatch ? energyMatch[1] : undefined;
}
