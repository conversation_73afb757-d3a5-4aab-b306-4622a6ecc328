import { invoke } from '@tauri-apps/api/core';
import { open } from '@tauri-apps/plugin-dialog';
import {
  AudioFile,
  AudioMetadata,
  DirectoryFilter,
  DirectoryStructure,
  ScanProgress,
  DirectoryInfo,
  DEFAULT_DIRECTORY_FILTER,
} from '../types/audio';

export class AudioService {
  private static instance: AudioService;
  private activeScanSessions = new Set<string>();

  static getInstance(): AudioService {
    if (!AudioService.instance) {
      AudioService.instance = new AudioService();
    }
    return AudioService.instance;
  }

  /**
   * Open a directory selection dialog
   */
  async selectDirectory(): Promise<string | null> {
    try {
      const result = await open({
        directory: true,
        multiple: false,
        title: 'Select Music Library Directory',
      });
      
      return Array.isArray(result) ? result[0] : result;
    } catch (error) {
      console.error('Error selecting directory:', error);
      return null;
    }
  }

  /**
   * Start scanning a directory for audio files
   */
  async startDirectoryScan(
    rootPath: string,
    filter: DirectoryFilter = DEFAULT_DIRECTORY_FILTER
  ): Promise<string> {
    try {
      const sessionId = await invoke<string>('scan_directory', {
        rootPath,
        filter,
      });
      
      this.activeScanSessions.add(sessionId);
      return sessionId;
    } catch (error) {
      throw new Error(`Failed to start directory scan: ${error}`);
    }
  }

  /**
   * Get the progress of an active scan
   */
  async getScanProgress(sessionId: string): Promise<ScanProgress> {
    try {
      return await invoke<ScanProgress>('get_scan_progress', { sessionId });
    } catch (error) {
      throw new Error(`Failed to get scan progress: ${error}`);
    }
  }

  /**
   * Get the results of a completed scan
   */
  async getScanResults(sessionId: string): Promise<AudioFile[]> {
    try {
      const results = await invoke<AudioFile[]>('get_scan_results', { sessionId });
      this.activeScanSessions.delete(sessionId);
      return results;
    } catch (error) {
      throw new Error(`Failed to get scan results: ${error}`);
    }
  }

  /**
   * Cancel an active scan
   */
  async cancelScan(sessionId: string): Promise<void> {
    try {
      await invoke('cancel_scan', { sessionId });
      this.activeScanSessions.delete(sessionId);
    } catch (error) {
      throw new Error(`Failed to cancel scan: ${error}`);
    }
  }

  /**
   * Clean up a scan session
   */
  async cleanupScanSession(sessionId: string): Promise<void> {
    try {
      await invoke('cleanup_scan_session', { sessionId });
      this.activeScanSessions.delete(sessionId);
    } catch (error) {
      console.error('Failed to cleanup scan session:', error);
    }
  }

  /**
   * Get directory structure with audio file information
   */
  async getDirectoryStructure(
    rootPath: string,
    filter: DirectoryFilter = DEFAULT_DIRECTORY_FILTER
  ): Promise<DirectoryStructure> {
    try {
      return await invoke<DirectoryStructure>('get_directory_structure', {
        rootPath,
        filter,
      });
    } catch (error) {
      throw new Error(`Failed to get directory structure: ${error}`);
    }
  }

  /**
   * Extract metadata from a single audio file
   */
  async extractAudioMetadata(filePath: string): Promise<AudioMetadata> {
    try {
      return await invoke<AudioMetadata>('extract_audio_metadata', { filePath });
    } catch (error) {
      throw new Error(`Failed to extract audio metadata: ${error}`);
    }
  }

  /**
   * Check if a file is a supported audio format
   */
  async isSupportedAudioFormat(filePath: string): Promise<boolean> {
    try {
      return await invoke<boolean>('is_supported_audio_format', { filePath });
    } catch (error) {
      console.error('Error checking audio format:', error);
      return false;
    }
  }

  /**
   * Validate that a directory path exists and is accessible
   */
  async validateDirectoryPath(path: string): Promise<boolean> {
    try {
      return await invoke<boolean>('validate_directory_path', { path });
    } catch (error) {
      console.error('Error validating directory path:', error);
      return false;
    }
  }

  /**
   * Get basic information about a directory
   */
  async getDirectoryInfo(path: string): Promise<DirectoryInfo> {
    try {
      return await invoke<DirectoryInfo>('get_directory_info', { path });
    } catch (error) {
      throw new Error(`Failed to get directory info: ${error}`);
    }
  }

  /**
   * Create a backup of a file before modification
   */
  async createBackup(filePath: string): Promise<string> {
    try {
      return await invoke<string>('create_backup', { filePath });
    } catch (error) {
      throw new Error(`Failed to create backup: ${error}`);
    }
  }

  /**
   * Move an audio file to a new location
   */
  async moveAudioFile(fromPath: string, toPath: string): Promise<void> {
    try {
      await invoke('move_audio_file', { fromPath, toPath });
    } catch (error) {
      throw new Error(`Failed to move audio file: ${error}`);
    }
  }

  /**
   * Copy an audio file to a new location
   */
  async copyAudioFile(fromPath: string, toPath: string): Promise<void> {
    try {
      await invoke('copy_audio_file', { fromPath, toPath });
    } catch (error) {
      throw new Error(`Failed to copy audio file: ${error}`);
    }
  }

  /**
   * Monitor scan progress with a callback
   */
  async monitorScanProgress(
    sessionId: string,
    onProgress: (progress: ScanProgress) => void,
    onComplete: (results: AudioFile[]) => void,
    onError: (error: string) => void,
    intervalMs: number = 1000
  ): Promise<void> {
    const checkProgress = async () => {
      try {
        const progress = await this.getScanProgress(sessionId);
        onProgress(progress);

        if (progress.percentage >= 100 || progress.status.includes('completed')) {
          try {
            const results = await this.getScanResults(sessionId);
            onComplete(results);
          } catch (error) {
            onError(`Failed to get results: ${error}`);
          }
          return;
        }

        if (progress.status.includes('Error') || progress.status.includes('Cancelled')) {
          onError(progress.status);
          return;
        }

        // Continue monitoring
        setTimeout(checkProgress, intervalMs);
      } catch (error) {
        onError(`Failed to check progress: ${error}`);
      }
    };

    checkProgress();
  }

  /**
   * Get all active scan sessions
   */
  getActiveScanSessions(): string[] {
    return Array.from(this.activeScanSessions);
  }

  /**
   * Clean up all active scan sessions
   */
  async cleanupAllSessions(): Promise<void> {
    const sessions = Array.from(this.activeScanSessions);
    await Promise.all(
      sessions.map(sessionId => this.cleanupScanSession(sessionId))
    );
  }
}

// Export singleton instance
export const audioService = AudioService.getInstance();
